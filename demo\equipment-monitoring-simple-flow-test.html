<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备监控台 - 简单光点效果测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a2e;
            color: white;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .chart-container {
            width: 100%;
            height: 500px;
            border: 1px solid #333;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            padding: 8px 16px;
            background: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0052a3;
        }
        .status {
            background: #2a2a3e;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>设备监控台 - 简单光点效果测试</h1>
        
        <div class="controls">
            <button onclick="toggleFlowDots()">切换光点效果</button>
            <button onclick="changeFlowColor('#ff0000')">红色光点</button>
            <button onclick="changeFlowColor('#00ff00')">绿色光点</button>
            <button onclick="changeFlowColor('#0000ff')">蓝色光点</button>
            <button onclick="changeFlowColor('#ffff00')">黄色光点</button>
        </div>
        
        <div class="status" id="status">
            状态: 准备就绪
        </div>

        <div class="chart-container" id="chart"></div>
    </div>

    <script src="../dist/index.js"></script>
    <script>
        let chart = null;
        let flowEnabled = true;

        // 初始化图表
        function initChart() {
            const option = {
                backgroundColor: '#0f1419',
                connectionLineColor: '#537895',
                deviceBorderColor: '#2c5aa0',
                deviceBackgroundColor: 'rgba(44, 90, 160, 0.3)',
                deviceTextColor: '#ffffff',
                flowDotsEnabled: true,
                flowDotColor: '#00ff88',
                customDevices: [
                    { id: 'server1', name: '服务器1', x: 100, y: 150, status: 'running' },
                    { id: 'switch1', name: '交换机1', x: 300, y: 150, status: 'running' },
                    { id: 'router1', name: '路由器1', x: 500, y: 150, status: 'running' },
                    { id: 'firewall1', name: '防火墙1', x: 700, y: 150, status: 'running' },
                    { id: 'server2', name: '服务器2', x: 300, y: 50, status: 'running' },
                    { id: 'server3', name: '服务器3', x: 300, y: 250, status: 'running' }
                ],
                connections: [
                    { from: 'server1', to: 'switch1' },
                    { from: 'switch1', to: 'router1' },
                    { from: 'router1', to: 'firewall1' },
                    { from: 'switch1', to: 'server2' },
                    { from: 'switch1', to: 'server3' }
                ]
            };

            chart = new CI.EquipmentMonitoring(document.getElementById('chart'), option);
            updateStatus('图表初始化完成，光点效果已启用');
        }

        // 切换光点效果
        function toggleFlowDots() {
            flowEnabled = !flowEnabled;
            
            if (chart) {
                const currentOption = chart.getOption();
                currentOption.flowDotsEnabled = flowEnabled;
                chart.setOption(currentOption);
            }
            
            updateStatus(`光点效果: ${flowEnabled ? '开启' : '关闭'}`);
        }

        // 更改光点颜色
        function changeFlowColor(color) {
            if (chart) {
                const currentOption = chart.getOption();
                currentOption.flowDotColor = color;
                chart.setOption(currentOption);
            }
            
            updateStatus(`光点颜色已更改为: ${color}`);
        }

        // 更新状态
        function updateStatus(message) {
            const status = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            status.textContent = `[${timestamp}] ${message}`;
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            setTimeout(() => {
                initChart();
            }, 100);
        });
    </script>
</body>
</html>
