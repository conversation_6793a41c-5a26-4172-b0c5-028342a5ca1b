<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备监控台 - 光点流动效果测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a2e;
            color: white;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-panel {
            background: #16213e;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .chart-container {
            width: 100%;
            height: 600px;
            border: 1px solid #333;
            border-radius: 8px;
            overflow: hidden;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .control-group label {
            font-size: 12px;
            color: #ccc;
        }
        button {
            padding: 8px 16px;
            background: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0052a3;
        }
        .status {
            background: #2a2a3e;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>设备监控台 - 光点流动效果测试</h1>
        
        <div class="test-panel">
            <h3>测试说明</h3>
            <p>此页面用于测试设备监控台控件的光点流动效果。光点应该沿着连接线流动，模拟数据流或能量流的效果。</p>
            
            <div class="controls">
                <button onclick="loadTestData1()">加载测试数据1 (简单连接)</button>
                <button onclick="loadTestData2()">加载测试数据2 (复杂网络)</button>
                <button onclick="loadTestData3()">加载测试数据3 (星型拓扑)</button>
                <button onclick="toggleFlowEffect()">切换光点效果</button>
            </div>
            
            <div class="status" id="status">
                状态: 准备就绪
            </div>
        </div>

        <div class="chart-container" id="chart"></div>
    </div>

    <script src="../dist/index.js"></script>
    <script>
        let chart = null;
        let flowEnabled = true;

        // 初始化图表
        function initChart() {
            const option = {
                backgroundColor: '#0f1419',
                connectionLineColor: '#537895',
                deviceBorderColor: '#2c5aa0',
                deviceBackgroundColor: 'rgba(44, 90, 160, 0.3)',
                deviceTextColor: '#ffffff',
                flowDotsEnabled: flowEnabled,
                devices: []
            };

            chart = new CI.EquipmentMonitoring(document.getElementById('chart'), option);
            updateStatus('图表初始化完成');
        }

        // 测试数据1 - 简单连接
        function loadTestData1() {
            const devices = [
                { id: 'server1', name: '服务器1', x: 100, y: 100, status: 'running' },
                { id: 'switch1', name: '交换机1', x: 300, y: 100, status: 'running' },
                { id: 'router1', name: '路由器1', x: 500, y: 100, status: 'running' },
                { id: 'firewall1', name: '防火墙1', x: 700, y: 100, status: 'running' }
            ];

            const connections = [
                { from: 'server1', to: 'switch1' },
                { from: 'switch1', to: 'router1' },
                { from: 'router1', to: 'firewall1' }
            ];

            updateChart(devices, connections);
            updateStatus('已加载测试数据1 - 简单连接');
        }

        // 测试数据2 - 复杂网络
        function loadTestData2() {
            const devices = [
                { id: 'core1', name: '核心交换机', x: 400, y: 200, status: 'running' },
                { id: 'server1', name: '服务器1', x: 200, y: 100, status: 'running' },
                { id: 'server2', name: '服务器2', x: 600, y: 100, status: 'running' },
                { id: 'switch1', name: '交换机1', x: 200, y: 300, status: 'running' },
                { id: 'switch2', name: '交换机2', x: 600, y: 300, status: 'running' },
                { id: 'router1', name: '路由器1', x: 100, y: 400, status: 'running' },
                { id: 'router2', name: '路由器2', x: 700, y: 400, status: 'running' }
            ];

            const connections = [
                { from: 'core1', to: 'server1' },
                { from: 'core1', to: 'server2' },
                { from: 'core1', to: 'switch1' },
                { from: 'core1', to: 'switch2' },
                { from: 'switch1', to: 'router1' },
                { from: 'switch2', to: 'router2' },
                { from: 'server1', to: 'switch1' },
                { from: 'server2', to: 'switch2' }
            ];

            updateChart(devices, connections);
            updateStatus('已加载测试数据2 - 复杂网络');
        }

        // 测试数据3 - 星型拓扑
        function loadTestData3() {
            const devices = [
                { id: 'center', name: '中心节点', x: 400, y: 250, status: 'running' }
            ];

            const connections = [];
            
            // 创建星型拓扑
            for (let i = 0; i < 8; i++) {
                const angle = (i * 45) * Math.PI / 180;
                const radius = 150;
                const x = 400 + Math.cos(angle) * radius;
                const y = 250 + Math.sin(angle) * radius;
                
                const deviceId = `node${i + 1}`;
                devices.push({
                    id: deviceId,
                    name: `节点${i + 1}`,
                    x: x,
                    y: y,
                    status: 'running'
                });
                
                connections.push({ from: 'center', to: deviceId });
            }

            updateChart(devices, connections);
            updateStatus('已加载测试数据3 - 星型拓扑');
        }

        // 更新图表
        function updateChart(devices, connections) {
            if (chart) {
                const option = {
                    backgroundColor: '#0f1419',
                    connectionLineColor: '#537895',
                    deviceBorderColor: '#2c5aa0',
                    deviceBackgroundColor: 'rgba(44, 90, 160, 0.3)',
                    deviceTextColor: '#ffffff',
                    flowDotsEnabled: flowEnabled,
                    devices: devices,
                    connections: connections
                };
                
                chart.setOption(option);
            }
        }

        // 切换光点效果
        function toggleFlowEffect() {
            flowEnabled = !flowEnabled;
            updateStatus(`光点效果: ${flowEnabled ? '开启' : '关闭'}`);
            
            // 重新加载当前数据以应用效果
            if (chart && chart.getOption && chart.getOption().devices) {
                const currentOption = chart.getOption();
                currentOption.flowDotsEnabled = flowEnabled;
                chart.setOption(currentOption);
            }
        }

        // 更新状态
        function updateStatus(message) {
            const status = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            status.textContent = `[${timestamp}] ${message}`;
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            setTimeout(() => {
                initChart();
                loadTestData1(); // 默认加载第一个测试数据
            }, 100);
        });
    </script>
</body>
</html>
