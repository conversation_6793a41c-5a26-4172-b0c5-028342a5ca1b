<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备监控台组件 - 增强功能测试</title>
    <script src="../dist/CI.Web.Plugins.Bulletin.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            color: #dcdcdc;
            min-height: 100vh;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .container {
            width: 100%;
            height: 80vh;
            border: 2px solid #333;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }
        
        .controls {
            margin-bottom: 20px;
            padding: 20px;
            background: rgba(26, 26, 46, 0.9);
            border-radius: 12px;
            border: 1px solid #0f3460;
            backdrop-filter: blur(10px);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .control-section {
            background: rgba(22, 33, 62, 0.8);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #0f3460;
        }
        
        .control-section h3 {
            color: #e94560;
            margin: 0 0 15px 0;
            font-size: 16px;
            text-align: center;
            border-bottom: 1px solid #0f3460;
            padding-bottom: 8px;
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            color: #e94560;
            font-weight: 500;
            font-size: 12px;
        }
        
        .control-group input, .control-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #0f3460;
            border-radius: 6px;
            background-color: #16213e;
            color: #dcdcdc;
            font-size: 12px;
        }
        
        .control-group input[type="color"] {
            height: 40px;
            padding: 2px;
            cursor: pointer;
        }
        
        .control-group input[type="range"] {
            margin-bottom: 5px;
        }
        
        .range-value {
            text-align: center;
            font-size: 11px;
            color: #2ecc71;
            font-weight: bold;
        }
        
        .control-group button {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 6px;
            background: linear-gradient(45deg, #e94560, #d63447);
            color: white;
            cursor: pointer;
            font-weight: 500;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .control-group button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(233, 69, 96, 0.4);
        }
        
        h1 {
            color: #e94560;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 0 0 20px rgba(233, 69, 96, 0.5);
            font-size: 2.5rem;
        }
        
        .feature-highlight {
            background: rgba(46, 204, 113, 0.1);
            border: 1px solid #2ecc71;
            border-radius: 6px;
            padding: 10px;
            margin-top: 10px;
            font-size: 11px;
            color: #2ecc71;
        }
        
        .status-info {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: #2ecc71;
            padding: 10px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <h1>🔧 设备监控台组件 - 增强功能测试</h1>
    
    <div class="controls">
        <!-- 背景控制 -->
        <div class="control-section">
            <h3>🎨 背景设置</h3>
            <div class="control-group">
                <label>容器背景颜色:</label>
                <input type="color" id="backgroundColor" value="#1a1a2e">
            </div>
            <div class="control-group">
                <label>背景透明度:</label>
                <input type="range" id="backgroundOpacity" min="0" max="1" step="0.1" value="0.9">
                <div class="range-value" id="opacityValue">90%</div>
            </div>
            <div class="control-group">
                <label>布局背景颜色:</label>
                <input type="color" id="layoutBackgroundColor" value="#16213e">
            </div>
            <div class="feature-highlight">
                ✨ 新功能：支持背景透明度调节，可与彩色背景完美融合
            </div>
        </div>
        
        <!-- 设备样式控制 -->
        <div class="control-section">
            <h3>📱 设备样式</h3>
            <div class="control-group">
                <label>设备背景颜色:</label>
                <input type="color" id="deviceBackgroundColor" value="#0f3460">
            </div>
            <div class="control-group">
                <label>连接线颜色:</label>
                <input type="color" id="connectionLineColor" value="#537895">
            </div>
            <div class="control-group">
                <label>文字颜色:</label>
                <input type="color" id="textColor" value="#dcdcdc">
            </div>
            <div class="feature-highlight">
                ✨ 新功能：动态设置设备背景和连接线颜色
            </div>
        </div>
        
        <!-- 标题控制 -->
        <div class="control-section">
            <h3>📝 标题设置</h3>
            <div class="control-group">
                <label>显示标题:</label>
                <input type="checkbox" id="showHeader" checked>
            </div>
            <div class="control-group">
                <label>标题颜色:</label>
                <input type="color" id="headerColor" value="#e94560">
            </div>
            <div class="control-group">
                <label>标题大小:</label>
                <input type="range" id="headerFontSize" min="16" max="48" value="24">
                <div class="range-value" id="fontSizeValue">24px</div>
            </div>
        </div>
        
        <!-- 功能控制 -->
        <div class="control-section">
            <h3>⚙️ 功能控制</h3>
            <div class="control-group">
                <button onclick="updateComponent()">🔄 更新组件</button>
            </div>
            <div class="control-group">
                <button onclick="togglePolling()">⏱️ 切换轮询</button>
            </div>
            <div class="control-group">
                <button onclick="addRandomDevice()">➕ 添加设备</button>
            </div>
            <div class="control-group">
                <button onclick="resetToDefault()">🔄 重置默认</button>
            </div>
            <div class="feature-highlight">
                ✨ 改进：updateZoom支持图层缩放更新
            </div>
        </div>
    </div>

    <div class="container" id="container">
        <div class="status-info" id="statusInfo">
            设备数量: <span id="deviceCount">0</span> | 
            缩放比例: <span id="scaleInfo">1.0</span> | 
            轮询: <span id="pollingStatus">开启</span>
        </div>
    </div>

    <script>
        let componentInstance = null;
        let pollingEnabled = true;
        let deviceCount = 0;

        // 初始化组件配置
        const initialOption = {
            showHeader: true,
            title: '产线设备实时监控',
            backgroundColor: '#1a1a2e',
            backgroundOpacity: 0.9,
            layoutBackgroundColor: '#16213e',
            deviceBackgroundColor: '#0f3460',
            connectionLineColor: '#537895',
            textColor: '#dcdcdc',
            headerColor: '#e94560',
            headerFontSize: 24,
            headerAlign: 'center',
            fontFamily: 'Noto Sans SC, sans-serif',
            enablePolling: true,
            pollingInterval: 3000,
            customDevices: [
                { id: 'dev-01', name: '放板机', x: 50, y: 50, status: 'running', connections: ['dev-02'], tooltipData: { '型号': 'LDR-2000', '厂商': 'A-Tech' } },
                { id: 'dev-02', name: '激光打码', x: 200, y: 50, status: 'running', connections: ['dev-03'], icons: { camera: true } },
                { id: 'dev-03', name: '线路前处理', x: 350, y: 50, status: 'idle', connections: ['dev-04'] },
                { id: 'dev-04', name: '暂存机', x: 500, y: 50, status: 'alarm', connections: ['dev-05'] },
                { id: 'dev-05', name: '整平机', x: 650, y: 50, status: 'running', connections: [] }
            ]
        };

        deviceCount = initialOption.customDevices.length;

        // 初始化组件
        function initComponent() {
            componentInstance = new CI.Web.Plugins.Bulletin.equipmentMonitoring({
                container: document.getElementById('container'),
                option: initialOption
            });
            updateStatusInfo();
        }

        // 更新组件
        function updateComponent() {
            const newOption = {
                ...initialOption,
                backgroundColor: document.getElementById('backgroundColor').value,
                backgroundOpacity: parseFloat(document.getElementById('backgroundOpacity').value),
                layoutBackgroundColor: document.getElementById('layoutBackgroundColor').value,
                deviceBackgroundColor: document.getElementById('deviceBackgroundColor').value,
                connectionLineColor: document.getElementById('connectionLineColor').value,
                textColor: document.getElementById('textColor').value,
                headerColor: document.getElementById('headerColor').value,
                headerFontSize: parseInt(document.getElementById('headerFontSize').value),
                showHeader: document.getElementById('showHeader').checked,
                enablePolling: pollingEnabled
            };

            if (componentInstance) {
                componentInstance.updateOption(newOption);
            }
            updateStatusInfo();
        }

        // 切换轮询
        function togglePolling() {
            pollingEnabled = !pollingEnabled;
            updateComponent();
        }

        // 添加随机设备
        function addRandomDevice() {
            const newDevice = {
                id: `dev-${Date.now()}`,
                name: `设备${Math.floor(Math.random() * 100)}`,
                x: Math.random() * 600 + 50,
                y: Math.random() * 300 + 50,
                status: ['running', 'idle', 'alarm'][Math.floor(Math.random() * 3)],
                connections: [],
                icons: Math.random() > 0.7 ? { camera: true } : {}
            };
            
            initialOption.customDevices.push(newDevice);
            deviceCount = initialOption.customDevices.length;
            updateComponent();
        }

        // 重置为默认设置
        function resetToDefault() {
            document.getElementById('backgroundColor').value = '#1a1a2e';
            document.getElementById('backgroundOpacity').value = '0.9';
            document.getElementById('layoutBackgroundColor').value = '#16213e';
            document.getElementById('deviceBackgroundColor').value = '#0f3460';
            document.getElementById('connectionLineColor').value = '#537895';
            document.getElementById('textColor').value = '#dcdcdc';
            document.getElementById('headerColor').value = '#e94560';
            document.getElementById('headerFontSize').value = '24';
            document.getElementById('showHeader').checked = true;
            
            updateSliderValues();
            updateComponent();
        }

        // 更新滑块显示值
        function updateSliderValues() {
            document.getElementById('opacityValue').textContent = 
                Math.round(document.getElementById('backgroundOpacity').value * 100) + '%';
            document.getElementById('fontSizeValue').textContent = 
                document.getElementById('headerFontSize').value + 'px';
        }

        // 更新状态信息
        function updateStatusInfo() {
            document.getElementById('deviceCount').textContent = deviceCount;
            document.getElementById('pollingStatus').textContent = pollingEnabled ? '开启' : '关闭';
            
            // 模拟获取缩放信息
            setTimeout(() => {
                if (componentInstance && componentInstance.currentScale) {
                    document.getElementById('scaleInfo').textContent = 
                        (componentInstance.currentScale * 100).toFixed(0) + '%';
                }
            }, 100);
        }

        // 监听滑块变化
        document.getElementById('backgroundOpacity').addEventListener('input', updateSliderValues);
        document.getElementById('headerFontSize').addEventListener('input', updateSliderValues);

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            updateSliderValues();
            initComponent();
        });
    </script>
</body>
</html>
