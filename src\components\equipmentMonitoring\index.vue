<template>
  <div class="equipment-monitoring-container" :style="containerStyle">
    
    <header v-if="option.showHeader" class="equipment-header">
      <h1 :style="headerStyle">{{ option.title || '产线设备实时监控' }}</h1>
    </header>
    <main class="device-layout" ref="deviceLayout" :style="layoutStyle">
      <div ref="zoomContainer" class="zoom-container">
        <!-- 设备将由 JavaScript 动态生成 -->
      </div>
    </main>

    <!-- 弹窗/模态窗口 -->
    <!-- <div v-if="showModal" class="modal" @click="closeModal">
      <div class="modal-content" @click.stop>
        <span class="close-button" @click="closeModal">&times;</span>
        <h2>{{ modalTitle }}</h2>
        <div class="modal-body">
          <div class="tabs">
            <button 
              v-for="tab in tabs" 
              :key="tab.key"
              class="tab-link" 
              :class="{ active: activeTab === tab.key }"
              @click="activeTab = tab.key"
            >
              {{ tab.label }}
            </button>
          </div>
          <div v-if="activeTab === 'params'" class="tab-content active">
            <p v-for="param in currentDeviceParams" :key="param.key">
              {{ param.label }}: <span>{{ param.value }}</span> {{ param.unit }}
            </p>
          </div>
          <div v-if="activeTab === 'maintenance'" class="tab-content active">
            <p>上次保养时间: <span>{{ currentDeviceMaintenance.date }}</span></p>
            <p>保养负责人: <span>{{ currentDeviceMaintenance.by }}</span></p>
          </div>
          <div v-if="activeTab === 'alarm'" class="tab-content active">
            <p>报警代码: <span>{{ currentDeviceAlarm.code }}</span></p>
            <p>报警信息: <span>{{ currentDeviceAlarm.message }}</span></p>
            <p>发生时间: <span>{{ currentDeviceAlarm.time }}</span></p>
          </div>
        </div>
      </div>
    </div> -->

    <!-- 摄像头视频弹窗 -->  <!-- 实际项目中这里会嵌入视频流 -->
    <!-- <div v-if="showCameraModal" class="modal" @click="closeCameraModal">
      <div class="modal-content camera-view" @click.stop>
        <span class="close-button" @click="closeCameraModal">&times;</span>
        <h2>{{ cameraTitle }}</h2>
        <div class="modal-body">
        
          <img src="https://via.placeholder.com/800x450.png?text=Camera+Feed" alt="Camera Feed" style="width: 100%;">
        </div>
      </div>
    </div> -->

    <!-- 工具提示 -->
    <div 
      v-if="showTooltip" 
      class="device-tooltip" 
      :style="tooltipStyle"
    >
      <div v-for="(value, key) in tooltipData" :key="key">
        <strong>{{ key }}:</strong> {{ value }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'equipmentMonitoringType', // 这个名称可以随意，不能和src\echart\packages下的名称相同
  props: {
    option: {
      type: Object,
      default: () => ({})
    },
    // 数据源相关props（从父组件传入）
    dataType: {
      type: Number,
      default: 0
    },
    dataMethod: {
      type: String,
      default: "get"
    },
    url: {
      type: String
    },
    styleSizeName: String,
    dataQuery: String,
    dataHeader: String,
    dataFormatter: Function,
    dataSet: String,
    time: {
      type: Number,
      default: 0
    },
    data: {
      type: [Object, String, Array]
    }
  },
  data() {
    return {
      devices: [],
      showModal: false,
      showCameraModal: false,
      showTooltip: false,
      modalTitle: '',
      cameraTitle: '',
      activeTab: 'params',
      currentDevice: null,
      tooltipData: {},
      tooltipPosition: { x: 0, y: 0 },
      pollingTimer: null,
      currentScale: 1,
      currentOffset: { x: 0, y: 0 },
      tabs: [
        { key: 'params', label: '关键参数' },
        { key: 'maintenance', label: '保养信息' },
        { key: 'alarm', label: '报警详情' }
      ]
    }
  },
  computed: {
    containerStyle() {
      const bgColor = this.option.backgroundColor || '#1a1a2e'
      const opacity = this.option.backgroundOpacity !== undefined ? this.option.backgroundOpacity : 1

      return {
        width: '100%',
        height: '100%',
        backgroundColor: opacity < 1 ? 'transparent' : bgColor,
        background: opacity < 1 ? `rgba(${this.hexToRgb(bgColor)}, ${opacity})` : bgColor,
        color: this.option.textColor || '#dcdcdc',
        fontFamily: this.option.fontFamily || 'Noto Sans SC, sans-serif'
      }
    },
    headerStyle() {
      return {
        fontSize: this.option.headerFontSize || '2.5rem',
        color: this.option.headerColor || '#e94560',
        textAlign: this.option.headerAlign || 'center',
        textShadow: `0 0 10px ${this.option.headerColor || '#e94560'}`,
        marginBottom: '20px'
      }
    },
    tooltipStyle() {
      return {
        position: 'absolute',
        left: this.tooltipPosition.x + 'px',
        top: this.tooltipPosition.y + 'px',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '8px 12px',
        borderRadius: '4px',
        pointerEvents: 'none',
        zIndex: 1000,
        fontSize: '12px'
      }
    },
    layoutStyle() {
      const bgColor = this.option.layoutBackgroundColor || '#16213e'
      const opacity = this.option.backgroundOpacity !== undefined ? this.option.backgroundOpacity : 1

      return {
        position: 'relative',
        width: '100%',
        height: this.option.showHeader ? 'calc(100% - 80px)' : '100%',
        overflow: 'hidden',
        //background:'transparent'
        backgroundColor: opacity < 1 ? 'transparent' : bgColor,
        background: opacity < 1 ? `rgba(${this.hexToRgb(bgColor)}, ${opacity})` : bgColor,
        // border: '1px solid #0f3460',
        // borderRadius: '10px',
        // backgroundImage: 'radial-gradient(circle, #0f3460 1px, transparent 1px)',
        backgroundSize: '20px 20px'
      }
    },
    currentDeviceParams() {
      if (!this.currentDevice) return []
      return [
        { key: 'temp', label: '温度', value: (Math.random() * 20 + 60).toFixed(1), unit: '°C' },
        { key: 'pressure', label: '压力', value: (Math.random() * 50 + 100).toFixed(1), unit: 'kPa' },
        { key: 'speed', label: '运行速度', value: (Math.random() * 10 + 5).toFixed(1), unit: 'm/min' }
      ]
    },
    currentDeviceMaintenance() {
      return {
        date: '2024-01-15',
        by: '张工程师'
      }
    },
    currentDeviceAlarm() {
      return {
        code: 'E001',
        message: '温度超限',
        time: '2024-01-17 14:30:25'
      }
    }
  },
  mounted() {
     this.loadFontAwesome()
    this.initializeLayout()
    this.startPolling()
  },
  beforeDestroy() {
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer)
    }
    window.removeEventListener('resize', this.updateZoom)
  },
  methods: {
    // 加载Font Awesome图标库
    loadFontAwesome() {
      if (!document.querySelector('link[href*="font-awesome"]')) {
        const link = document.createElement('link')
        link.rel = 'stylesheet'
        link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css'
         //link.href = './font-awesome.css'
        document.head.appendChild(link)
      }
    },
    // 辅助方法：将十六进制颜色转换为RGB
    hexToRgb(hex) {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
      return result ?
        `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` :
        '26, 26, 46' // 默认值
    },
    async initializeLayout() {
      try {
        const deviceData = await this.getDeviceData()
        this.devices = deviceData
        this.$nextTick(() => {
          this.renderDevices()
          this.updateZoom()
          window.addEventListener('resize', this.updateZoom)
        })
      } catch (error) {
        console.error('Failed to load device data:', error)
      }
    },
    async getDeviceData() {
      // 优先使用从父组件传入的数据（动态数据源）
      if (this.data && Array.isArray(this.data) && this.data.length > 0) {
        return this.data
      }

      // 如果有自定义数据源，使用自定义数据
      if (this.option.customDevices && this.option.customDevices.length > 0) {
        return this.option.customDevices
      }

      // 否则使用默认的模拟数据
      return this.getMockDeviceData()
    },
    getMockDeviceData() {
      const mockData = [
        { id: 'dev-01', name: '放板机', x: 50, y: 50, status: 'running', connections: ['dev-02'], tooltipData: { '型号': 'LDR-2000', '厂商': 'A-Tech', '投入日期': '2022-08-15' } },
        { id: 'dev-02', name: '激光打码', x: 200, y: 50, status: 'running', connections: ['dev-03'], icons: { camera: true }, tooltipData: { '型号': 'LM-500', '功率': '50W', '厂商': 'B-Laser' } },
        { id: 'dev-03', name: '线路前处理', x: 350, y: 50, status: 'running', connections: ['dev-04'], tooltipData: { '处理方式': '等离子清洗', '厂商': 'C-Process' } },
        { id: 'dev-04', name: '暂存机', x: 500, y: 50, status: 'idle', connections: ['dev-05'] },
        { id: 'dev-05', name: '整平机', x: 650, y: 50, status: 'running', connections: ['dev-06'] },
        { id: 'dev-06', name: '涂布机', x: 800, y: 50, status: 'running', connections: ['dev-07'] },
        { id: 'dev-07', name: '隧道炉', x: 950, y: 50, status: 'running', connections: ['dev-08'] },
        { id: 'dev-08', name: '整平机', x: 1100, y: 50, status: 'running', connections: ['dev-09'] },
        { id: 'dev-09', name: '暂存机', x: 1250, y: 50, status: 'idle', connections: ['dev-10'] },
        { id: 'dev-10', name: '线路DI', x: 1400, y: 50, status: 'running', connections: ['dev-11'], icons: { camera: true } },
        { id: 'dev-11', name: 'NG暂存机', x: 1550, y: 50, status: 'idle', connections: ['dev-12'] },
        { id: 'dev-12', name: '显影蚀刻退膜', x: 1700, y: 50, status: 'running', connections: ['dev-13'], icons: { star: true } },
        { id: 'dev-13', name: 'AOI', x: 1850, y: 50, status: 'running', connections: ['dev-14'] },
        { id: 'dev-14', name: '转角机1', x: 1850, y: 180, status: 'running', connections: ['dev-15'] },
        { id: 'dev-15', name: '侧边一体机', x: 1850, y: 310, status: 'running', connections: ['dev-16'] },
        { id: 'dev-16', name: '打靶', x: 1850, y: 440, status: 'running', connections: ['dev-17'] },
        { id: 'dev-17', name: '转角机3', x: 1850, y: 570, status: 'running', connections: ['dev-18'] },
        { id: 'dev-18', name: '阻焊前处理', x: 1700, y: 570, status: 'running', connections: ['dev-19'] },
        { id: 'dev-19', name: '暂存机', x: 1550, y: 570, status: 'idle', connections: ['dev-20'] },
        { id: 'dev-20', name: '阻焊涂布', x: 1400, y: 570, status: 'running', connections: ['dev-21'] },
        { id: 'dev-21', name: '隧道炉', x: 1250, y: 570, status: 'running', connections: ['dev-22'] },
        { id: 'dev-22', name: '先进先出暂存机', x: 1100, y: 570, status: 'idle', connections: ['dev-23'] },
        { id: 'dev-23', name: '暂存机', x: 950, y: 570, status: 'running', connections: ['dev-24'], icons: { camera: true } },
        { id: 'dev-24', name: '阻焊DI', x: 800, y: 570, status: 'idle', connections: ['dev-25'] },
        { id: 'dev-25', name: 'NG暂存机', x: 650, y: 570, status: 'running', connections: ['dev-26'], icons: { star: true } },
        { id: 'dev-26', name: '显影', x: 500, y: 570, status: 'idle', connections: ['dev-27'] },
        { id: 'dev-27', name: '暂存机', x: 500, y: 440, status: 'idle', connections: ['dev-28'] },
        { id: 'dev-28', name: '文字', x: 350, y: 440, status: 'running', connections: ['dev-29'] },
        { id: 'dev-29', name: '隧道炉', x: 200, y: 440, status: 'running', connections: ['dev-30'] },
        { id: 'dev-30', name: '收板机', x: 50, y: 440, status: 'running', connections: [] }
      ]
      
      // 随机化状态
      mockData.forEach(device => {
        const rand = Math.random()
        if (rand < 0.15) { device.status = 'alarm' }
        else if (rand < 0.4) { device.status = 'idle' }
        else { device.status = 'running' }
      })
      
      return Promise.resolve(mockData)
    },
    renderDevices() {
      const zoomContainer = this.$refs.zoomContainer
      if (!zoomContainer) return

      // 清空容器
      zoomContainer.innerHTML = ''

      // 渲染设备
      this.devices.forEach(device => {
        const devElement = document.createElement('div')
        devElement.className = `device ${device.status}`
        devElement.id = device.id
        devElement.style.left = `${device.x}px`
        devElement.style.top = `${device.y}px`
        devElement.style.position = 'absolute'
        devElement.style.width = '120px'
        devElement.style.height = '70px'
        devElement.style.border = '2px solid'
        devElement.style.borderRadius = '8px'
        devElement.style.display = 'flex'
        devElement.style.flexDirection = 'column'
        devElement.style.justifyContent = 'center'
        devElement.style.alignItems = 'center'
        devElement.style.cursor = 'pointer'
        devElement.style.fontSize = '12px'
        devElement.style.fontWeight = 'bold'
        devElement.style.textAlign = 'center'
        devElement.style.transition = 'all 0.3s ease'

        // 设置基础样式（参考原插件样式）
        devElement.style.backgroundColor = this.option.deviceBackgroundColor || '#0f3460'
        devElement.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.4)'

        // 根据状态设置边框颜色
        if (device.status === 'running') {
          devElement.style.borderColor = '#2ecc71'
          devElement.style.color = this.option.textColor || '#dcdcdc'
        } else if (device.status === 'idle') {
          devElement.style.borderColor = '#3498db'
          devElement.style.color = this.option.textColor || '#dcdcdc'
        } else if (device.status === 'alarm') {
          devElement.style.borderColor = '#e74c3c'
          devElement.style.color = this.option.textColor || '#dcdcdc'
          devElement.style.animation = 'pulse-alarm 1s infinite'
        }

        const name = document.createElement('div')
        name.className = 'device-name'
        name.textContent = device.name
        name.style.marginBottom = '5px'

        const icons = document.createElement('div')
        icons.className = 'device-icons'
        icons.style.display = 'flex'
        icons.style.gap = '5px'

        let iconHtml = '<i class="fa-solid fa-triangle-exclamation" style="display: none; color: #e74c3c; font-size: 0.8rem; opacity: 0.7;"></i>'
        if (device.icons?.camera) {
          iconHtml += '<i class="fa-solid fa-video camera-btn" style="color: #3498db; font-size: 0.8rem; opacity: 0.7;"></i>'
        }
        if (device.icons?.star) {
          iconHtml += '<i class="fa-solid fa-star" style="color: #f1c40f; font-size: 0.8rem; opacity: 0.7;"></i>'
        }
        icons.innerHTML = iconHtml
        icons.style.position = 'absolute'
        icons.style.top = '5px'
        icons.style.right = '5px'
        icons.style.display = 'flex'
        icons.style.gap = '5px'

        devElement.appendChild(name)
        devElement.appendChild(icons)

        // 添加事件监听器
        if (device.tooltipData) {
          devElement.addEventListener('mouseover', (event) => {
            this.showTooltip = true
            this.tooltipData = device.tooltipData
            this.updateTooltipPosition(event)
          })

          devElement.addEventListener('mouseout', () => {
            this.showTooltip = false
          })

          devElement.addEventListener('mousemove', (event) => {
            this.updateTooltipPosition(event)
          })
        }

        devElement.addEventListener('click', (event) => {
          if (event.target.classList.contains('camera-btn')) {
            this.openCameraModal(device)
          } else {
            this.openDeviceModal(device)
          }
        })

        zoomContainer.appendChild(devElement)
      })

      // 绘制连接线
      this.drawConnections()
    },
    drawConnections() {
      const zoomContainer = this.$refs.zoomContainer
      if (!zoomContainer) return

      const svgNS = "http://www.w3.org/2000/svg"
      const svg = document.createElementNS(svgNS, 'svg')
      svg.setAttribute('width', '100%')
      svg.setAttribute('height', '100%')
      svg.style.position = 'absolute'
      svg.style.top = '0'
      svg.style.left = '0'
      svg.style.zIndex = '-1'

      // 处理连接线 - 支持两种格式
      const connections = []

      // 格式1: 独立的connections数组
      if (this.option.connections && Array.isArray(this.option.connections)) {
        this.option.connections.forEach(conn => {
          connections.push({ from: conn.from, to: conn.to })
        })
      }

      // 格式2: 设备内的connections属性
      this.devices.forEach(startDevice => {
        if (startDevice.connections) {
          startDevice.connections.forEach(endDeviceId => {
            connections.push({ from: startDevice.id, to: endDeviceId })
          })
        }
      })

      // 绘制所有连接线
      connections.forEach(conn => {
        const startDevice = this.devices.find(d => d.id === conn.from)
        const endDevice = this.devices.find(d => d.id === conn.to)
        if (!startDevice || !endDevice) return

        const startEl = document.getElementById(startDevice.id)
        const endEl = document.getElementById(endDevice.id)
        if (!startEl || !endEl) return

        const line = document.createElementNS(svgNS, 'path')
        const pathId = `path_${startDevice.id}_${endDevice.id}`
        line.setAttribute('id', pathId)

        const startX = startEl.offsetLeft + startEl.offsetWidth / 2
        const startY = startEl.offsetTop + startEl.offsetHeight / 2
        const endX = endEl.offsetLeft + endEl.offsetWidth / 2
        const endY = endEl.offsetTop + endEl.offsetHeight / 2

        let d = ''
        const isHorizontal = Math.abs(startY - endY) < 5
        const isVertical = Math.abs(startX - endX) < 5

        if (isHorizontal || isVertical) {
          d = `M ${startX} ${startY} L ${endX} ${endY}`
        } else {
          d = `M ${startX} ${startY} L ${startX} ${endY} L ${endX} ${endY}`
        }

        line.setAttribute('d', d)
        line.setAttribute('stroke', this.option.connectionLineColor || '#537895')
        line.setAttribute('stroke-width', '2')
        line.setAttribute('fill', 'none')
        svg.appendChild(line)

        // 添加流动点（使用SVG实现更好的兼容性）
        if (this.option.flowDotsEnabled !== false) {
          this.createFlowingDots(svg, d)
        }
      })

      zoomContainer.appendChild(svg)
    },
    createFlowingDots(svg, pathData) {
      const svgNS = "http://www.w3.org/2000/svg"
      const dotColor = this.option.flowDotColor || '#00ff88'

      // 创建多个流动点
      for (let i = 0; i < 3; i++) {
        const circle = document.createElementNS(svgNS, 'circle')
        circle.setAttribute('r', '4')
        circle.setAttribute('fill', dotColor)
        circle.setAttribute('opacity', '0')
        circle.style.filter = `drop-shadow(0 0 6px ${dotColor})`

        // 创建动画路径
        const animateMotion = document.createElementNS(svgNS, 'animateMotion')
        animateMotion.setAttribute('dur', '3s')
        animateMotion.setAttribute('repeatCount', 'indefinite')
        animateMotion.setAttribute('begin', `${i * 1}s`)

        // 创建路径元素
        const path = document.createElementNS(svgNS, 'path')
        path.setAttribute('d', pathData)
        animateMotion.appendChild(path)

        // 添加透明度动画
        const animateOpacity = document.createElementNS(svgNS, 'animate')
        animateOpacity.setAttribute('attributeName', 'opacity')
        animateOpacity.setAttribute('values', '0;1;1;0')
        animateOpacity.setAttribute('dur', '3s')
        animateOpacity.setAttribute('repeatCount', 'indefinite')
        animateOpacity.setAttribute('begin', `${i * 1}s`)

        circle.appendChild(animateMotion)
        circle.appendChild(animateOpacity)
        svg.appendChild(circle)
      }
    },
    updateZoom() {
      const layout = this.$refs.deviceLayout
      const zoomContainer = this.$refs.zoomContainer
      if (!layout || !zoomContainer) return

      const layoutWidth = layout.clientWidth
      const layoutHeight = layout.clientHeight

      // 如果没有设备数据，使用默认尺寸
      if (!this.devices || this.devices.length === 0) {
        zoomContainer.style.width = `${layoutWidth}px`
        zoomContainer.style.height = `${layoutHeight}px`
        zoomContainer.style.transform = 'translate(0px, 0px) scale(1)'
        return
      }

      // 计算内容边界
      let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity
      this.devices.forEach(d => {
        minX = Math.min(minX, d.x)
        minY = Math.min(minY, d.y)
        maxX = Math.max(maxX, d.x + 120) // 120是设备宽度
        maxY = Math.max(maxY, d.y + 70)  // 70是设备高度
      })

      // 添加边距
      const padding = 50
      const contentWidth = maxX - minX + padding * 2
      const contentHeight = maxY - minY + padding * 2

      // 计算缩放比例
      const scaleX = layoutWidth / contentWidth
      const scaleY = layoutHeight / contentHeight
      const scale = Math.min(scaleX, scaleY, 1)

      // 计算偏移量，确保内容居中
      const scaledWidth = contentWidth * scale
      const scaledHeight = contentHeight * scale
      const offsetX = (layoutWidth - scaledWidth) / 2 - (minX - padding) * scale
      const offsetY = (layoutHeight - scaledHeight) / 2 - (minY - padding) * scale

      // 应用变换
      zoomContainer.style.width = `${contentWidth}px`
      zoomContainer.style.height = `${contentHeight}px`
      zoomContainer.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${scale})`

      // 存储当前缩放信息，供其他方法使用
      this.currentScale = scale
      this.currentOffset = { x: offsetX, y: offsetY }
    },
    updateTooltipPosition(event) {
      this.tooltipPosition = {
        x: event.pageX + 15,
        y: event.pageY + 15
      }
    },
    openDeviceModal(device) {
      this.currentDevice = device
      this.modalTitle = `${device.name} - 设备详情`
      this.activeTab = device.status === 'alarm' ? 'alarm' : 'params'
      this.showModal = true
    },
    openCameraModal(device) {
      this.cameraTitle = `${device.name} - 实时监控`
      this.showCameraModal = true
    },
    closeModal() {
      this.showModal = false
      this.currentDevice = null
    },
    closeCameraModal() {
      this.showCameraModal = false
    },
    startPolling() {
      if (this.option.enablePolling !== false) {
        console.log("====startPolling=====")
        this.pollingTimer = setInterval(() => {
          this.updateDeviceStatuses()
        }, this.option.pollingInterval || 3000)
      }
    },
    async updateDeviceStatuses() {
      try {
        const newDevices = await this.getDeviceData()
        newDevices.forEach(newDevice => {
          const deviceElement = document.getElementById(newDevice.id)
          if (!deviceElement) return

          const currentDevice = this.devices.find(d => d.id === newDevice.id)
          if (currentDevice && currentDevice.status === newDevice.status) {
            return
          }

          // 更新状态
          deviceElement.classList.remove('running', 'idle', 'alarm')
          deviceElement.classList.add(newDevice.status)

          // 更新样式（保持原插件风格）
          deviceElement.style.backgroundColor = this.option.deviceBackgroundColor || '#0f3460'
          deviceElement.style.animation = ''

          if (newDevice.status === 'running') {
            deviceElement.style.borderColor = '#2ecc71'
            deviceElement.style.color = this.option.textColor || '#dcdcdc'
          } else if (newDevice.status === 'idle') {
            deviceElement.style.borderColor = '#3498db'
            deviceElement.style.color = this.option.textColor || '#dcdcdc'
          } else if (newDevice.status === 'alarm') {
            deviceElement.style.borderColor = '#e74c3c'
            deviceElement.style.color = this.option.textColor || '#dcdcdc'
            deviceElement.style.animation = 'pulse-alarm 1s infinite'
          }

          // 更新告警图标
          const alarmIcon = deviceElement.querySelector('.fa-triangle-exclamation')
          if (alarmIcon) {
            alarmIcon.style.display = newDevice.status === 'alarm' ? 'inline-block' : 'none'
          }

          // 更新内存中的设备数据
          if (currentDevice) {
            currentDevice.status = newDevice.status
          }
        })
      } catch (error) {
        console.error('Error updating device statuses:', error)
      }
    },
    // 重新渲染组件（当配置变化时调用）
    refresh() {
      this.$nextTick(() => {
        this.renderDevices()
        this.updateZoom()
      })
    }
  },
  watch: {
    // 缩放BOX监听 刷新界面
    styleSizeName(){
      console.log("styleSizeName change")
       this.refresh()
    },
    // 监听配置变化，自动重新渲染
    'option.deviceBackgroundColor'() {
      this.refresh()
    },
    'option.connectionLineColor'() {
      this.refresh()
    },
    'option.backgroundOpacity'() {
      this.refresh()
    },
    'option.layoutBackgroundColor'() {
      this.refresh()
    },
      'option.customDevices'() {
      this.refresh()
    },
    // 监听数据变化（动态数据源）
    data: {
      handler(newData) {
        if (newData && Array.isArray(newData)) {
          //this.initializeLayout()
          this.startPolling()
          
        }
      },
      deep: true
    }
  }
}
</script>

<style scoped>
/* CSS变量定义 */
:root {
  --bg-color: #1a1a2e;
  --primary-color: #16213e;
  --secondary-color: #0f3460;
  --font-color: #e94560;
  --text-color: #dcdcdc;
  --border-color: #0f3460;
  --running-color: #2ecc71;
  --idle-color: #3498db;
  --alarm-color: #e74c3c;
  --arrow-color: #537895;
}

.equipment-monitoring-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-family: 'Noto Sans SC', sans-serif;
}

header {
  padding: 20px;
  text-align: center;
}

.equipment-header {
  padding: 20px;
  text-align: center;
}

.device-layout {
  position: relative;
  width: 100%;
  overflow: hidden;
  flex-grow: 1;
}

.zoom-container {
  position: relative;
  transform-origin: top left;
  transition: transform 0.3s ease-in-out;
}

.device {
  position: absolute;
  transition: all 0.3s ease;
  background-color: var(--secondary-color);
  border: 2px solid var(--idle-color);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

.device:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
}

.device.running {
  border-color: var(--running-color);
}

.device.alarm {
  border-color: var(--alarm-color);
  animation: pulse-alarm 1s infinite;
}

.device-name {
  font-weight: 500;
  font-size: 0.9rem;
}

.modal {
  display: block;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: #0f3460;
  backdrop-filter: blur(5px);
}

.modal-content {
  background-color: var(--primary-color);
  margin: 15% auto;
  padding: 20px;
  border: 1px solid var(--border-color);
  width: 80%;
  max-width: 600px;
  border-radius: 10px;
  position: relative;
  animation: slide-in 0.5s ease-out;
  color: var(--text-color);
}

.camera-view {
  max-width: 850px;
}

.close-button {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close-button:hover,
.close-button:focus {
  color: var(--text-color);
  text-decoration: none;
}

.modal h2 {
  color: var(--font-color);
  margin-bottom: 20px;
}

.tabs {
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 20px;
}

.tab-link {
  background: none;
  border: none;
  padding: 10px 20px;
  cursor: pointer;
  color: var(--text-color);
  font-size: 1rem;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.tab-link.active,
.tab-link:hover {
  opacity: 1;
  border-bottom: 2px solid var(--font-color);
}

.tab-content {
  display: none;
  animation: fade-in 0.5s;
}

.tab-content.active {
  display: block;
}

.tab-content p {
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.tab-content p span {
  color: var(--running-color);
  font-weight: 500;
}

.device-tooltip {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  pointer-events: none;
  z-index: 1000;
  font-size: 12px;
}

/* 脉冲告警动画 */
@keyframes pulse-alarm {
  0% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
  }
}

/* 流动点样式 - 使用SVG动画实现 */

/* 连接线样式 */
.connector {
  stroke: var(--arrow-color);
  stroke-width: 2;
  fill: none;
}

/* 模态窗口动画 */
@keyframes slide-in {
  from {
    top: -100px;
    opacity: 0;
  }
  to {
    top: 0;
    opacity: 1;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
